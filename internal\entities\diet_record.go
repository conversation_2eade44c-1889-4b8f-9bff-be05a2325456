package entities

import (
	"time"
)

// DietRecord 饮食记录实体 - 包含业务逻辑和数据库映射
type DietRecord struct {
	ID           int64     `gorm:"column:id;primaryKey;autoIncrement;type:bigint" json:"id"`
	UserID       int64     `gorm:"column:user_id;type:bigint;not null;comment:用户ID" json:"userId"`
	Date         time.Time `gorm:"column:date;type:date;not null;comment:记录日期" json:"date"`
	Time         time.Time `gorm:"column:time;type:time;not null;comment:记录时间" json:"time"`
	MealType     string    `gorm:"column:meal_type;type:varchar(20);not null;comment:餐次类型: breakfast/lunch/dinner/snacks" json:"mealType"`
	Remark       *string   `gorm:"column:remark;type:varchar(200);comment:备注信息" json:"remark"`
	TotalCalorie float64   `gorm:"column:total_calorie;type:decimal(8,2);not null;comment:总热量(千卡)" json:"totalCalorie"`
	CreatedAt    time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt    time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`

	// 关联字段 - 一对多关联DietRecordFood
	DietRecordFoods []DietRecordFood `gorm:"foreignKey:DietRecordID;references:ID" json:"dietRecordFoods,omitempty"`
}

// DietRecordFood 饮食记录食物明细实体 - 包含业务逻辑和数据库映射
type DietRecordFood struct {
	ID           int64     `gorm:"column:id;primaryKey;autoIncrement;type:bigint" json:"id"`
	DietRecordID int64     `gorm:"column:diet_record_id;type:bigint;not null;comment:关联饮食记录ID" json:"dietRecordId"`
	FoodID       int64     `gorm:"column:food_id;type:bigint;not null;comment:食物ID" json:"foodId"`
	FoodName     string    `gorm:"column:food_name;type:varchar(50);not null;comment:食物名称" json:"foodName"`
	Amount       float64   `gorm:"column:amount;type:decimal(8,2);not null;comment:食物数量" json:"amount"`
	Unit         string    `gorm:"column:unit;type:varchar(20);not null;comment:计量单位" json:"unit"`
	Calories     float64   `gorm:"column:calories;type:decimal(8,2);not null;comment:热量(千卡)" json:"calories"`
	Protein      float64   `gorm:"column:protein;type:decimal(8,2);not null;comment:蛋白质(g)" json:"protein"`
	Fat          float64   `gorm:"column:fat;type:decimal(8,2);not null;comment:脂肪(g)" json:"fat"`
	Carbs        float64   `gorm:"column:carbs;type:decimal(8,2);not null;comment:碳水化合物(g)" json:"carbs"`
	Grams        float64   `gorm:"column:grams;type:decimal(8,2);not null;comment:食物克数" json:"grams"`
	CreatedAt    time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`

	// 关联字段
	DietRecord *DietRecord `gorm:"foreignKey:DietRecordID;references:ID" json:"dietRecord,omitempty"`
	Food       *Food       `gorm:"foreignKey:FoodID;references:ID" json:"food,omitempty"`
}

// TableName 指定DietRecord表名
func (DietRecord) TableName() string {
	return "diet_records"
}

// TableName 指定DietRecordFood表名
func (DietRecordFood) TableName() string {
	return "diet_record_foods"
}

// NewDietRecord 创建新饮食记录实体
func NewDietRecord(userID int64, date time.Time, mealType string) *DietRecord {
	now := time.Now()
	return &DietRecord{
		UserID:       userID,
		Date:         date,
		Time:         now,
		MealType:     mealType,
		TotalCalorie: 0.0,
		CreatedAt:    now,
		UpdatedAt:    now,
	}
}

// NewDietRecordFood 创建新饮食记录食物明细实体
func NewDietRecordFood(dietRecordID, foodID int64, foodName string, amount float64, unit string) *DietRecordFood {
	now := time.Now()
	return &DietRecordFood{
		DietRecordID: dietRecordID,
		FoodID:       foodID,
		FoodName:     foodName,
		Amount:       amount,
		Unit:         unit,
		CreatedAt:    now,
	}
}

// SetRemark 设置备注信息
func (dr *DietRecord) SetRemark(remark string) {
	dr.Remark = &remark
	dr.UpdatedAt = time.Now()
}

// SetDateTime 设置日期和时间
func (dr *DietRecord) SetDateTime(date, time time.Time) {
	dr.Date = date
	dr.Time = time
	dr.UpdatedAt = time.Now()
}

// GetDateTime 获取完整的日期时间
func (dr *DietRecord) GetDateTime() time.Time {
	// 将日期和时间合并为完整的datetime
	year, month, day := dr.Date.Date()
	hour, min, sec := dr.Time.Clock()
	return time.Date(year, month, day, hour, min, sec, 0, dr.Date.Location())
}

// IsValidMealType 验证餐次类型是否有效
func (dr *DietRecord) IsValidMealType() bool {
	validTypes := []string{"breakfast", "lunch", "dinner", "snacks"}
	for _, validType := range validTypes {
		if dr.MealType == validType {
			return true
		}
	}
	return false
}

// CalculateTotalCalorie 计算总热量（基于关联的食物明细）
func (dr *DietRecord) CalculateTotalCalorie() {
	total := 0.0
	for _, food := range dr.DietRecordFoods {
		total += food.Calories
	}
	dr.TotalCalorie = total
	dr.UpdatedAt = time.Now()
}

// AddFood 添加食物明细
func (dr *DietRecord) AddFood(food *DietRecordFood) {
	dr.DietRecordFoods = append(dr.DietRecordFoods, *food)
	dr.CalculateTotalCalorie()
}

// RemoveFood 移除食物明细
func (dr *DietRecord) RemoveFood(foodID int64) {
	for i, food := range dr.DietRecordFoods {
		if food.ID == foodID {
			dr.DietRecordFoods = append(dr.DietRecordFoods[:i], dr.DietRecordFoods[i+1:]...)
			break
		}
	}
	dr.CalculateTotalCalorie()
}

// GetTotalNutrition 获取总营养信息
func (dr *DietRecord) GetTotalNutrition() (protein, fat, carbs float64) {
	for _, food := range dr.DietRecordFoods {
		protein += food.Protein
		fat += food.Fat
		carbs += food.Carbs
	}
	return
}

// HasRemark 检查是否有备注
func (dr *DietRecord) HasRemark() bool {
	return dr.Remark != nil && *dr.Remark != ""
}

// SetNutritionInfo 设置食物明细的营养信息
func (drf *DietRecordFood) SetNutritionInfo(calories, protein, fat, carbs, grams float64) {
	drf.Calories = calories
	drf.Protein = protein
	drf.Fat = fat
	drf.Carbs = carbs
	drf.Grams = grams
}

// GetCaloriesPerUnit 获取每单位的热量
func (drf *DietRecordFood) GetCaloriesPerUnit() float64 {
	if drf.Amount == 0 {
		return 0
	}
	return drf.Calories / drf.Amount
}

// GetProteinPerUnit 获取每单位的蛋白质含量
func (drf *DietRecordFood) GetProteinPerUnit() float64 {
	if drf.Amount == 0 {
		return 0
	}
	return drf.Protein / drf.Amount
}

// GetFatPerUnit 获取每单位的脂肪含量
func (drf *DietRecordFood) GetFatPerUnit() float64 {
	if drf.Amount == 0 {
		return 0
	}
	return drf.Fat / drf.Amount
}

// GetCarbsPerUnit 获取每单位的碳水化合物含量
func (drf *DietRecordFood) GetCarbsPerUnit() float64 {
	if drf.Amount == 0 {
		return 0
	}
	return drf.Carbs / drf.Amount
}

// UpdateAmount 更新食物数量并重新计算营养信息
func (drf *DietRecordFood) UpdateAmount(newAmount float64) {
	if drf.Amount == 0 {
		return
	}
	
	ratio := newAmount / drf.Amount
	drf.Amount = newAmount
	drf.Calories *= ratio
	drf.Protein *= ratio
	drf.Fat *= ratio
	drf.Carbs *= ratio
	drf.Grams *= ratio
}
