package service

import (
	"context"
	v1 "shikeyinxiang/api/v1"
)

// IFoodService 食物服务接口，定义了所有与食物管理相关的业务能力
type IFoodService interface {
	// CreateFood 创建食物
	CreateFood(ctx context.Context, req *v1.FoodCreateReq) (*v1.FoodResponse, error)

	// GetFood 获取食物详情
	GetFood(ctx context.Context, id int) (*v1.FoodResponse, error)

	// UpdateFood 更新食物
	UpdateFood(ctx context.Context, req *v1.FoodUpdateReq) (*v1.FoodResponse, error)

	// DeleteFood 删除食物
	DeleteFood(ctx context.Context, id int) error

	// ListFoods 获取食物列表（支持分页、搜索、筛选）
	ListFoods(ctx context.Context, req *v1.FoodQueryReq) (*v1.FoodListResponse, error)

	// SearchFoods 搜索食物
	SearchFoods(ctx context.Context, keyword string, categoryID *int, page, size int) (*v1.FoodListResponse, error)
}

// IFoodCategoryService 食物分类服务接口，定义了所有与食物分类管理相关的业务能力
type IFoodCategoryService interface {
	// CreateCategory 创建食物分类
	CreateCategory(ctx context.Context, req *v1.FoodCategoryCreateReq) (*v1.FoodCategoryResponse, error)

	// GetCategory 获取食物分类详情
	GetCategory(ctx context.Context, id int) (*v1.FoodCategoryResponse, error)

	// UpdateCategory 更新食物分类
	UpdateCategory(ctx context.Context, req *v1.FoodCategoryUpdateReq) (*v1.FoodCategoryResponse, error)

	// DeleteCategory 删除食物分类
	DeleteCategory(ctx context.Context, id int) error

	// ListCategories 获取食物分类列表（支持分页、搜索）
	ListCategories(ctx context.Context, req *v1.FoodCategoryQueryReq) (*v1.FoodCategoryListResponse, error)

	// GetAllCategories 获取所有食物分类（按排序顺序）
	GetAllCategories(ctx context.Context) ([]*v1.FoodCategoryResponse, error)

	// UpdateCategorySortOrder 更新分类排序顺序
	UpdateCategorySortOrder(ctx context.Context, id int, sortOrder int) error

	// SearchCategories 搜索食物分类
	SearchCategories(ctx context.Context, keyword string, page, size int) (*v1.FoodCategoryListResponse, error)
}

// IFoodManagementService 食物管理综合服务接口，整合食物和分类管理功能
type IFoodManagementService interface {
	IFoodService
	IFoodCategoryService

	// GenerateUploadURL 生成食物图片上传URL
	GenerateUploadURL(ctx context.Context, req *v1.UploadImageReq) (*v1.UploadImageResponse, error)
}
