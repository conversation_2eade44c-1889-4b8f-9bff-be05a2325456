package logic

import (
	"context"
	"errors"
	"fmt"
	"time"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/repositories"
	"shikeyinxiang/internal/service"
)

// dietRecordLogic 饮食记录业务逻辑实现
type dietRecordLogic struct {
	dietRecordRepo     repositories.IDietRecordRepo
	dietRecordFoodRepo repositories.IDietRecordFoodRepo
	foodRepo           repositories.IFoodRepo
}

// NewDietRecordLogic 创建饮食记录业务逻辑实例
func NewDietRecordLogic(
	dietRecordRepo repositories.IDietRecordRepo,
	dietRecordFoodRepo repositories.IDietRecordFoodRepo,
	foodRepo repositories.IFoodRepo,
) service.IDietRecordService {
	return &dietRecordLogic{
		dietRecordRepo:     dietRecordRepo,
		dietRecordFoodRepo: dietRecordFoodRepo,
		foodRepo:           foodRepo,
	}
}

// 确保 dietRecordLogic 实现了 IDietRecordService 接口
var _ service.IDietRecordService = &dietRecordLogic{}

// CreateDietRecord 创建饮食记录
func (d *dietRecordLogic) CreateDietRecord(ctx context.Context, userID int64, req *v1.DietRecordCreateReq) (*v1.DietRecordResponse, error) {
	// 参数验证
	if err := d.validateCreateRequest(req); err != nil {
		return nil, err
	}

	// 解析日期和时间
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return nil, &ParameterError{Field: "date", Message: "invalid date format, expected YYYY-MM-DD"}
	}

	timeValue, err := time.Parse("15:04:05", req.Time)
	if err != nil {
		return nil, &ParameterError{Field: "time", Message: "invalid time format, expected HH:MM:SS"}
	}

	// 验证餐次类型
	if !d.isValidMealType(req.MealType) {
		return nil, &InvalidMealTypeError{MealType: req.MealType}
	}

	// 检查是否已存在相同日期和餐次的记录
	exists, err := d.dietRecordRepo.ExistsByUserAndDate(userID, date, req.MealType)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to check existing diet record: %w", err)
	}
	if exists {
		return nil, &DuplicateDietRecordError{
			UserID:   userID,
			Date:     req.Date,
			MealType: req.MealType,
		}
	}

	// 验证食物数据
	if err := d.validateFoodList(req.Foods); err != nil {
		return nil, err
	}

	// 创建饮食记录实体
	dietRecord := entities.NewDietRecord(userID, date, req.MealType)
	dietRecord.SetDateTime(date, timeValue)
	if req.Remark != nil {
		dietRecord.SetRemark(*req.Remark)
	}

	// 创建食物明细实体
	var dietRecordFoods []*entities.DietRecordFood
	totalCalorie := 0.0

	for _, foodReq := range req.Foods {
		// 验证食物ID是否存在
		if _, err := d.foodRepo.GetByID(int(foodReq.FoodID)); err != nil {
			var foodNotFound *repositories.FoodNotFoundError
			if errors.As(err, &foodNotFound) {
				return nil, &FoodNotFoundError{FoodID: int(foodReq.FoodID)}
			}
			return nil, fmt.Errorf("logic: failed to validate food: %w", err)
		}

		// 创建食物明细
		dietRecordFood := entities.NewDietRecordFood(0, foodReq.FoodID, foodReq.FoodName, foodReq.Amount, foodReq.Unit)
		dietRecordFood.SetNutritionInfo(foodReq.Calories, foodReq.Protein, foodReq.Fat, foodReq.Carbs, foodReq.Grams)

		dietRecordFoods = append(dietRecordFoods, dietRecordFood)
		totalCalorie += foodReq.Calories
	}

	// 设置总热量
	dietRecord.TotalCalorie = totalCalorie

	// 使用事务保存数据
	if err := d.createDietRecordWithTransaction(dietRecord, dietRecordFoods); err != nil {
		return nil, fmt.Errorf("logic: failed to create diet record: %w", err)
	}

	// 获取完整的饮食记录信息
	createdRecord, err := d.dietRecordRepo.GetByIDWithFoods(dietRecord.ID)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get created diet record: %w", err)
	}

	return d.convertToDietRecordResponse(createdRecord), nil
}

// GetDietRecord 获取饮食记录详情
func (d *dietRecordLogic) GetDietRecord(ctx context.Context, userID int64, id int64) (*v1.DietRecordResponse, error) {
	if id <= 0 {
		return nil, &ParameterError{Field: "id", Message: "must be positive"}
	}

	// 检查权限
	hasAccess, err := d.dietRecordRepo.CheckOwnership(id, userID)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to check ownership: %w", err)
	}
	if !hasAccess {
		return nil, &DietRecordAccessDeniedError{DietRecordID: id, UserID: userID}
	}

	// 获取饮食记录
	dietRecord, err := d.dietRecordRepo.GetByIDWithFoods(id)
	if err != nil {
		var recordNotFound *repositories.DietRecordNotFoundError
		if errors.As(err, &recordNotFound) {
			return nil, &DietRecordNotFoundError{DietRecordID: id}
		}
		return nil, fmt.Errorf("logic: failed to get diet record: %w", err)
	}

	return d.convertToDietRecordResponse(dietRecord), nil
}

// UpdateDietRecord 更新饮食记录
func (d *dietRecordLogic) UpdateDietRecord(ctx context.Context, userID int64, req *v1.DietRecordUpdateReq) (*v1.DietRecordResponse, error) {
	// 参数验证
	if req.ID <= 0 {
		return nil, &ParameterError{Field: "id", Message: "must be positive"}
	}

	if err := d.validateUpdateRequest(req); err != nil {
		return nil, err
	}

	// 检查权限
	hasAccess, err := d.dietRecordRepo.CheckOwnership(req.ID, userID)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to check ownership: %w", err)
	}
	if !hasAccess {
		return nil, &DietRecordAccessDeniedError{DietRecordID: req.ID, UserID: userID}
	}

	// 获取现有记录
	existingRecord, err := d.dietRecordRepo.GetByIDWithFoods(req.ID)
	if err != nil {
		var recordNotFound *repositories.DietRecordNotFoundError
		if errors.As(err, &recordNotFound) {
			return nil, &DietRecordNotFoundError{DietRecordID: req.ID}
		}
		return nil, fmt.Errorf("logic: failed to get existing diet record: %w", err)
	}

	// 解析日期和时间
	date, err := time.Parse("2006-01-02", req.Date)
	if err != nil {
		return nil, &ParameterError{Field: "date", Message: "invalid date format, expected YYYY-MM-DD"}
	}

	timeValue, err := time.Parse("15:04:05", req.Time)
	if err != nil {
		return nil, &ParameterError{Field: "time", Message: "invalid time format, expected HH:MM:SS"}
	}

	// 验证餐次类型
	if !d.isValidMealType(req.MealType) {
		return nil, &InvalidMealTypeError{MealType: req.MealType}
	}

	// 验证食物数据
	if err := d.validateUpdateFoodList(req.Foods); err != nil {
		return nil, err
	}

	// 更新饮食记录基本信息
	existingRecord.SetDateTime(date, timeValue)
	existingRecord.MealType = req.MealType
	if req.Remark != nil {
		existingRecord.SetRemark(*req.Remark)
	} else {
		existingRecord.Remark = nil
	}

	// 准备新的食物明细
	var newDietRecordFoods []*entities.DietRecordFood
	totalCalorie := 0.0

	for _, foodReq := range req.Foods {
		// 验证食物ID是否存在
		if _, err := d.foodRepo.GetByID(int(foodReq.FoodID)); err != nil {
			var foodNotFound *repositories.FoodNotFoundError
			if errors.As(err, &foodNotFound) {
				return nil, &FoodNotFoundError{FoodID: int(foodReq.FoodID)}
			}
			return nil, fmt.Errorf("logic: failed to validate food: %w", err)
		}

		// 创建或更新食物明细
		var dietRecordFood *entities.DietRecordFood
		if foodReq.ID != nil && *foodReq.ID > 0 {
			// 更新现有明细
			dietRecordFood = &entities.DietRecordFood{ID: *foodReq.ID}
		} else {
			// 创建新明细
			dietRecordFood = entities.NewDietRecordFood(req.ID, foodReq.FoodID, foodReq.FoodName, foodReq.Amount, foodReq.Unit)
		}

		dietRecordFood.DietRecordID = req.ID
		dietRecordFood.FoodID = foodReq.FoodID
		dietRecordFood.FoodName = foodReq.FoodName
		dietRecordFood.Amount = foodReq.Amount
		dietRecordFood.Unit = foodReq.Unit
		dietRecordFood.SetNutritionInfo(foodReq.Calories, foodReq.Protein, foodReq.Fat, foodReq.Carbs, foodReq.Grams)

		newDietRecordFoods = append(newDietRecordFoods, dietRecordFood)
		totalCalorie += foodReq.Calories
	}

	// 设置总热量
	existingRecord.TotalCalorie = totalCalorie

	// 使用事务更新数据
	if err := d.updateDietRecordWithTransaction(existingRecord, newDietRecordFoods); err != nil {
		return nil, fmt.Errorf("logic: failed to update diet record: %w", err)
	}

	// 获取更新后的完整信息
	updatedRecord, err := d.dietRecordRepo.GetByIDWithFoods(req.ID)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get updated diet record: %w", err)
	}

	return d.convertToDietRecordResponse(updatedRecord), nil
}

// DeleteDietRecord 删除饮食记录
func (d *dietRecordLogic) DeleteDietRecord(ctx context.Context, userID int64, id int64) error {
	if id <= 0 {
		return &ParameterError{Field: "id", Message: "must be positive"}
	}

	// 检查权限
	hasAccess, err := d.dietRecordRepo.CheckOwnership(id, userID)
	if err != nil {
		return fmt.Errorf("logic: failed to check ownership: %w", err)
	}
	if !hasAccess {
		return &DietRecordAccessDeniedError{DietRecordID: id, UserID: userID}
	}

	// 删除饮食记录（级联删除食物明细）
	if err := d.dietRecordRepo.Delete(id); err != nil {
		return fmt.Errorf("logic: failed to delete diet record: %w", err)
	}

	return nil
}

// BatchDeleteDietRecords 批量删除饮食记录
func (d *dietRecordLogic) BatchDeleteDietRecords(ctx context.Context, userID int64, req *v1.DietRecordBatchDeleteReq) error {
	if len(req.IDs) == 0 {
		return &ParameterError{Field: "ids", Message: "cannot be empty"}
	}

	// 检查所有记录的权限
	for _, id := range req.IDs {
		hasAccess, err := d.dietRecordRepo.CheckOwnership(id, userID)
		if err != nil {
			return fmt.Errorf("logic: failed to check ownership for id %d: %w", id, err)
		}
		if !hasAccess {
			return &DietRecordAccessDeniedError{DietRecordID: id, UserID: userID}
		}
	}

	// 批量删除
	if err := d.dietRecordRepo.BatchDelete(req.IDs); err != nil {
		return fmt.Errorf("logic: failed to batch delete diet records: %w", err)
	}

	return nil
}

// ListDietRecords 获取饮食记录列表
func (d *dietRecordLogic) ListDietRecords(ctx context.Context, userID int64, req *v1.DietRecordQueryReq) (*v1.DietRecordListResponse, error) {
	// 设置默认分页参数
	page := 1
	size := 10
	if req.Current > 0 {
		page = req.Current
	}
	if req.Size > 0 {
		size = req.Size
	}

	offset := (page - 1) * size

	// 解析日期范围
	var startDate, endDate *time.Time
	if req.StartDate != nil {
		if parsed, err := time.Parse("2006-01-02", *req.StartDate); err != nil {
			return nil, &ParameterError{Field: "startDate", Message: "invalid date format, expected YYYY-MM-DD"}
		} else {
			startDate = &parsed
		}
	}
	if req.EndDate != nil {
		if parsed, err := time.Parse("2006-01-02", *req.EndDate); err != nil {
			return nil, &ParameterError{Field: "endDate", Message: "invalid date format, expected YYYY-MM-DD"}
		} else {
			endDate = &parsed
		}
	}

	// 验证日期范围
	if startDate != nil && endDate != nil && startDate.After(*endDate) {
		return nil, &InvalidDateRangeError{
			StartDate: *req.StartDate,
			EndDate:   *req.EndDate,
		}
	}

	// 验证餐次类型
	if req.MealType != nil && !d.isValidMealType(*req.MealType) {
		return nil, &InvalidMealTypeError{MealType: *req.MealType}
	}

	// 查询数据
	dietRecords, total, err := d.dietRecordRepo.SearchWithFoods(userID, startDate, endDate, req.MealType, offset, size)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to list diet records: %w", err)
	}

	// 转换响应
	var responses []*v1.DietRecordResponse
	for _, record := range dietRecords {
		responses = append(responses, d.convertToDietRecordResponse(record))
	}

	return &v1.DietRecordListResponse{
		Total:   total,
		Records: responses,
		Current: page,
		Size:    size,
	}, nil
}

// SearchDietRecords 搜索饮食记录
func (d *dietRecordLogic) SearchDietRecords(ctx context.Context, userID int64, req *v1.DietRecordQueryReq) (*v1.DietRecordListResponse, error) {
	// 搜索逻辑与列表查询相同
	return d.ListDietRecords(ctx, userID, req)
}

// GetDietRecordsByDate 获取指定日期的饮食记录
func (d *dietRecordLogic) GetDietRecordsByDate(ctx context.Context, userID int64, date string) ([]*v1.DietRecordResponse, error) {
	// 解析日期
	parsedDate, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, &ParameterError{Field: "date", Message: "invalid date format, expected YYYY-MM-DD"}
	}

	// 查询指定日期的记录
	dietRecords, err := d.dietRecordRepo.GetByDateWithFoods(userID, parsedDate)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get diet records by date: %w", err)
	}

	// 转换响应
	var responses []*v1.DietRecordResponse
	for _, record := range dietRecords {
		responses = append(responses, d.convertToDietRecordResponse(record))
	}

	return responses, nil
}

// GetNutritionStats 获取营养统计
func (d *dietRecordLogic) GetNutritionStats(ctx context.Context, userID int64, req *v1.NutritionStatsQueryReq) (*v1.NutritionStatsResponse, error) {
	// 解析日期范围
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return nil, &ParameterError{Field: "startDate", Message: "invalid date format, expected YYYY-MM-DD"}
	}

	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		return nil, &ParameterError{Field: "endDate", Message: "invalid date format, expected YYYY-MM-DD"}
	}

	// 验证日期范围
	if startDate.After(endDate) {
		return nil, &InvalidDateRangeError{
			StartDate: req.StartDate,
			EndDate:   req.EndDate,
		}
	}

	// 验证餐次类型
	if req.MealType != nil && !d.isValidMealType(*req.MealType) {
		return nil, &InvalidMealTypeError{MealType: *req.MealType}
	}

	// 获取营养统计
	stats, err := d.dietRecordRepo.GetNutritionStatsByDateRange(userID, startDate, endDate, req.MealType)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get nutrition stats: %w", err)
	}

	// 获取每日营养统计
	dailyStats, err := d.dietRecordRepo.GetDailyNutritionStats(userID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get daily nutrition stats: %w", err)
	}

	// 计算天数
	dayCount := int(endDate.Sub(startDate).Hours()/24) + 1

	// 计算平均值
	avgCalorie := 0.0
	avgProtein := 0.0
	avgFat := 0.0
	avgCarbs := 0.0
	if dayCount > 0 {
		avgCalorie = stats.TotalCalorie / float64(dayCount)
		avgProtein = stats.TotalProtein / float64(dayCount)
		avgFat = stats.TotalFat / float64(dayCount)
		avgCarbs = stats.TotalCarbs / float64(dayCount)
	}

	// 转换每日统计
	var dailyStatsResponse []v1.DailyNutritionStats
	for _, daily := range dailyStats {
		dailyStatsResponse = append(dailyStatsResponse, v1.DailyNutritionStats{
			Date:         daily.Date.Format("2006-01-02"),
			TotalCalorie: daily.TotalCalorie,
			TotalProtein: daily.TotalProtein,
			TotalFat:     daily.TotalFat,
			TotalCarbs:   daily.TotalCarbs,
			MealStats:    []v1.MealNutritionStats{}, // TODO: 实现餐次统计
		})
	}

	return &v1.NutritionStatsResponse{
		StartDate:    req.StartDate,
		EndDate:      req.EndDate,
		TotalCalorie: stats.TotalCalorie,
		TotalProtein: stats.TotalProtein,
		TotalFat:     stats.TotalFat,
		TotalCarbs:   stats.TotalCarbs,
		AvgCalorie:   avgCalorie,
		AvgProtein:   avgProtein,
		AvgFat:       avgFat,
		AvgCarbs:     avgCarbs,
		DayCount:     dayCount,
		DailyStats:   dailyStatsResponse,
	}, nil
}

// 验证创建请求
func (d *dietRecordLogic) validateCreateRequest(req *v1.DietRecordCreateReq) error {
	if req.Date == "" {
		return &ParameterError{Field: "date", Message: "is required"}
	}
	if req.Time == "" {
		return &ParameterError{Field: "time", Message: "is required"}
	}
	if req.MealType == "" {
		return &ParameterError{Field: "mealType", Message: "is required"}
	}
	if len(req.Foods) == 0 {
		return &EmptyFoodListError{}
	}
	return nil
}

// 验证更新请求
func (d *dietRecordLogic) validateUpdateRequest(req *v1.DietRecordUpdateReq) error {
	if req.Date == "" {
		return &ParameterError{Field: "date", Message: "is required"}
	}
	if req.Time == "" {
		return &ParameterError{Field: "time", Message: "is required"}
	}
	if req.MealType == "" {
		return &ParameterError{Field: "mealType", Message: "is required"}
	}
	if len(req.Foods) == 0 {
		return &EmptyFoodListError{}
	}
	return nil
}

// 验证食物列表
func (d *dietRecordLogic) validateFoodList(foods []v1.DietRecordFoodCreateReq) error {
	for i, food := range foods {
		if food.FoodID <= 0 {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].foodId", i), Message: "must be positive"}
		}
		if food.FoodName == "" {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].foodName", i), Message: "is required"}
		}
		if food.Amount <= 0 {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].amount", i), Message: "must be positive"}
		}
		if food.Unit == "" {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].unit", i), Message: "is required"}
		}
		if food.Calories < 0 {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].calories", i), Message: "cannot be negative"}
		}
		if food.Protein < 0 {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].protein", i), Message: "cannot be negative"}
		}
		if food.Fat < 0 {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].fat", i), Message: "cannot be negative"}
		}
		if food.Carbs < 0 {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].carbs", i), Message: "cannot be negative"}
		}
		if food.Grams < 0 {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].grams", i), Message: "cannot be negative"}
		}
	}
	return nil
}

// 验证更新食物列表
func (d *dietRecordLogic) validateUpdateFoodList(foods []v1.DietRecordFoodUpdateReq) error {
	for i, food := range foods {
		if food.FoodID <= 0 {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].foodId", i), Message: "must be positive"}
		}
		if food.FoodName == "" {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].foodName", i), Message: "is required"}
		}
		if food.Amount <= 0 {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].amount", i), Message: "must be positive"}
		}
		if food.Unit == "" {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].unit", i), Message: "is required"}
		}
		if food.Calories < 0 {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].calories", i), Message: "cannot be negative"}
		}
		if food.Protein < 0 {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].protein", i), Message: "cannot be negative"}
		}
		if food.Fat < 0 {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].fat", i), Message: "cannot be negative"}
		}
		if food.Carbs < 0 {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].carbs", i), Message: "cannot be negative"}
		}
		if food.Grams < 0 {
			return &InvalidFoodDataError{Field: fmt.Sprintf("foods[%d].grams", i), Message: "cannot be negative"}
		}
	}
	return nil
}

// 验证餐次类型
func (d *dietRecordLogic) isValidMealType(mealType string) bool {
	validTypes := []string{"breakfast", "lunch", "dinner", "snacks"}
	for _, validType := range validTypes {
		if mealType == validType {
			return true
		}
	}
	return false
}

// 使用事务创建饮食记录
func (d *dietRecordLogic) createDietRecordWithTransaction(dietRecord *entities.DietRecord, foods []*entities.DietRecordFood) error {
	// 这里应该使用数据库事务，但为了简化，先使用普通方法
	// TODO: 实现真正的事务处理

	// 创建饮食记录
	if err := d.dietRecordRepo.Create(dietRecord); err != nil {
		return err
	}

	// 设置食物明细的饮食记录ID
	for _, food := range foods {
		food.DietRecordID = dietRecord.ID
	}

	// 批量创建食物明细
	if err := d.dietRecordFoodRepo.BatchCreate(foods); err != nil {
		// 如果食物明细创建失败，删除已创建的饮食记录
		d.dietRecordRepo.Delete(dietRecord.ID)
		return err
	}

	return nil
}

// 使用事务更新饮食记录
func (d *dietRecordLogic) updateDietRecordWithTransaction(dietRecord *entities.DietRecord, foods []*entities.DietRecordFood) error {
	// 这里应该使用数据库事务，但为了简化，先使用普通方法
	// TODO: 实现真正的事务处理

	// 更新饮食记录
	if err := d.dietRecordRepo.Update(dietRecord); err != nil {
		return err
	}

	// 删除现有的食物明细
	if err := d.dietRecordFoodRepo.BatchDeleteByDietRecordID(dietRecord.ID); err != nil {
		return err
	}

	// 批量创建新的食物明细
	if err := d.dietRecordFoodRepo.BatchCreate(foods); err != nil {
		return err
	}

	return nil
}

// 转换为饮食记录响应
func (d *dietRecordLogic) convertToDietRecordResponse(dietRecord *entities.DietRecord) *v1.DietRecordResponse {
	response := &v1.DietRecordResponse{
		ID:           dietRecord.ID,
		UserID:       dietRecord.UserID,
		Date:         dietRecord.Date,
		Time:         dietRecord.Time,
		MealType:     dietRecord.MealType,
		Remark:       dietRecord.Remark,
		TotalCalorie: dietRecord.TotalCalorie,
		CreatedAt:    dietRecord.CreatedAt,
		UpdatedAt:    dietRecord.UpdatedAt,
	}

	// 转换食物明细
	for _, food := range dietRecord.DietRecordFoods {
		foodResponse := v1.DietRecordFoodResponse{
			ID:           food.ID,
			DietRecordID: food.DietRecordID,
			FoodID:       food.FoodID,
			FoodName:     food.FoodName,
			Amount:       food.Amount,
			Unit:         food.Unit,
			Calories:     food.Calories,
			Protein:      food.Protein,
			Fat:          food.Fat,
			Carbs:        food.Carbs,
			Grams:        food.Grams,
			CreatedAt:    food.CreatedAt,
		}
		response.DietRecordFoods = append(response.DietRecordFoods, foodResponse)
	}

	return response
}
