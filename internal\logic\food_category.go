package logic

import (
	"context"
	"errors"
	"fmt"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/repositories"
	"shikeyinxiang/internal/service"
)

// foodCategoryLogic 食物分类业务逻辑实现
type foodCategoryLogic struct {
	categoryRepo repositories.IFoodCategoryRepo
	foodRepo     repositories.IFoodRepo
}

// NewFoodCategoryLogic 创建食物分类业务逻辑实例
func NewFoodCategoryLogic(categoryRepo repositories.IFoodCategoryRepo, foodRepo repositories.IFoodRepo) service.IFoodCategoryService {
	return &foodCategoryLogic{
		categoryRepo: categoryRepo,
		foodRepo:     foodRepo,
	}
}

// 确保 foodCategoryLogic 实现了 IFoodCategoryService 接口
var _ service.IFoodCategoryService = &foodCategoryLogic{}

// CreateCategory 创建食物分类
func (f *foodCategoryLogic) CreateCategory(ctx context.Context, req *v1.FoodCategoryCreateReq) (*v1.FoodCategoryResponse, error) {
	// 参数验证
	if req.Name == "" {
		return nil, &ParameterError{Field: "name", Message: "is required"}
	}

	// 检查分类名称是否已存在
	exists, err := f.categoryRepo.ExistsByName(req.Name)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to check category name exists: %w", err)
	}
	if exists {
		return nil, &FoodCategoryAlreadyExistsError{Field: "name", Value: req.Name}
	}

	// 设置排序顺序
	sortOrder := req.SortOrder
	if sortOrder == 0 {
		// 如果没有指定排序顺序，设置为最大值+1
		maxSortOrder, err := f.categoryRepo.GetMaxSortOrder()
		if err != nil {
			return nil, fmt.Errorf("logic: failed to get max sort order: %w", err)
		}
		sortOrder = maxSortOrder + 1
	}

	// 创建分类实体
	category := entities.NewFoodCategory(req.Name)
	if req.Description != nil {
		category.SetDescription(*req.Description)
	}
	if req.Color != nil {
		category.SetColor(*req.Color)
	}
	category.SetSortOrder(sortOrder)

	// 保存分类
	if err := f.categoryRepo.Create(category); err != nil {
		// 检查是否是重复名称错误
		var categoryExists *repositories.FoodCategoryExistsError
		if errors.As(err, &categoryExists) {
			return nil, &FoodCategoryAlreadyExistsError{Field: categoryExists.Field, Value: categoryExists.Value}
		}
		return nil, fmt.Errorf("logic: failed to create category: %w", err)
	}

	return f.convertToCategoryResponse(category), nil
}

// GetCategory 获取食物分类详情
func (f *foodCategoryLogic) GetCategory(ctx context.Context, id int) (*v1.FoodCategoryResponse, error) {
	if id <= 0 {
		return nil, &ParameterError{Field: "id", Message: "must be positive"}
	}

	category, err := f.categoryRepo.GetByID(id)
	if err != nil {
		var categoryNotFound *repositories.FoodCategoryNotFoundError
		if errors.As(err, &categoryNotFound) {
			return nil, &FoodCategoryNotFoundError{CategoryID: id}
		}
		return nil, fmt.Errorf("logic: failed to get category: %w", err)
	}

	return f.convertToCategoryResponse(category), nil
}

// UpdateCategory 更新食物分类
func (f *foodCategoryLogic) UpdateCategory(ctx context.Context, req *v1.FoodCategoryUpdateReq) (*v1.FoodCategoryResponse, error) {
	// 参数验证
	if req.ID <= 0 {
		return nil, &ParameterError{Field: "id", Message: "must be positive"}
	}
	if req.Name == "" {
		return nil, &ParameterError{Field: "name", Message: "is required"}
	}

	// 验证分类是否存在
	category, err := f.categoryRepo.GetByID(req.ID)
	if err != nil {
		var categoryNotFound *repositories.FoodCategoryNotFoundError
		if errors.As(err, &categoryNotFound) {
			return nil, &FoodCategoryNotFoundError{CategoryID: req.ID}
		}
		return nil, fmt.Errorf("logic: failed to get category for update: %w", err)
	}

	// 检查分类名称是否已存在（排除自身）
	exists, err := f.categoryRepo.ExistsByNameExcludeID(req.Name, req.ID)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to check category name exists: %w", err)
	}
	if exists {
		return nil, &FoodCategoryAlreadyExistsError{Field: "name", Value: req.Name}
	}

	// 更新分类信息
	category.UpdateInfo(req.Name, req.Description, req.Color, req.SortOrder)

	// 保存更新
	if err := f.categoryRepo.Update(category); err != nil {
		// 检查是否是重复名称错误
		var categoryExists *repositories.FoodCategoryExistsError
		if errors.As(err, &categoryExists) {
			return nil, &FoodCategoryAlreadyExistsError{Field: categoryExists.Field, Value: categoryExists.Value}
		}
		return nil, fmt.Errorf("logic: failed to update category: %w", err)
	}

	return f.convertToCategoryResponse(category), nil
}

// DeleteCategory 删除食物分类
func (f *foodCategoryLogic) DeleteCategory(ctx context.Context, id int) error {
	if id <= 0 {
		return &ParameterError{Field: "id", Message: "must be positive"}
	}

	// 验证分类是否存在
	if _, err := f.categoryRepo.GetByID(id); err != nil {
		var categoryNotFound *repositories.FoodCategoryNotFoundError
		if errors.As(err, &categoryNotFound) {
			return &FoodCategoryNotFoundError{CategoryID: id}
		}
		return fmt.Errorf("logic: failed to get category for delete: %w", err)
	}

	// 检查是否有食物正在使用该分类
	count, err := f.foodRepo.CountByCategory(id)
	if err != nil {
		return fmt.Errorf("logic: failed to count foods by category: %w", err)
	}
	if count > 0 {
		return &CategoryInUseError{CategoryID: id, FoodCount: count}
	}

	// 删除分类
	if err := f.categoryRepo.Delete(id); err != nil {
		return fmt.Errorf("logic: failed to delete category: %w", err)
	}

	return nil
}

// ListCategories 获取食物分类列表
func (f *foodCategoryLogic) ListCategories(ctx context.Context, req *v1.FoodCategoryQueryReq) (*v1.FoodCategoryListResponse, error) {
	// 设置默认分页参数
	page := req.Current
	if page <= 0 {
		page = 1
	}
	size := req.Size
	if size <= 0 {
		size = 10
	}

	offset := (page - 1) * size

	var categories []*entities.FoodCategory
	var total int64
	var err error

	// 根据查询条件选择不同的查询方法
	if req.Keyword != "" {
		categories, total, err = f.categoryRepo.Search(req.Keyword, offset, size)
	} else {
		categories, total, err = f.categoryRepo.List(offset, size)
	}

	if err != nil {
		return nil, fmt.Errorf("logic: failed to list categories: %w", err)
	}

	// 转换为响应格式
	records := make([]*v1.FoodCategoryResponse, len(categories))
	for i, category := range categories {
		records[i] = f.convertToCategoryResponse(category)
	}

	return &v1.FoodCategoryListResponse{
		Total:   total,
		Records: records,
		Current: page,
		Size:    size,
	}, nil
}

// GetAllCategories 获取所有食物分类（按排序顺序）
func (f *foodCategoryLogic) GetAllCategories(ctx context.Context) ([]*v1.FoodCategoryResponse, error) {
	categories, err := f.categoryRepo.GetAllOrderBySortOrder()
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get all categories: %w", err)
	}

	// 转换为响应格式
	responses := make([]*v1.FoodCategoryResponse, len(categories))
	for i, category := range categories {
		responses[i] = f.convertToCategoryResponse(category)
	}

	return responses, nil
}

// UpdateCategorySortOrder 更新分类排序顺序
func (f *foodCategoryLogic) UpdateCategorySortOrder(ctx context.Context, id int, sortOrder int) error {
	if id <= 0 {
		return &ParameterError{Field: "id", Message: "must be positive"}
	}
	if sortOrder < 0 {
		return &ParameterError{Field: "sortOrder", Message: "cannot be negative"}
	}

	// 验证分类是否存在
	if _, err := f.categoryRepo.GetByID(id); err != nil {
		var categoryNotFound *repositories.FoodCategoryNotFoundError
		if errors.As(err, &categoryNotFound) {
			return &FoodCategoryNotFoundError{CategoryID: id}
		}
		return fmt.Errorf("logic: failed to get category for sort order update: %w", err)
	}

	// 更新排序顺序
	if err := f.categoryRepo.UpdateSortOrder(id, sortOrder); err != nil {
		return fmt.Errorf("logic: failed to update category sort order: %w", err)
	}

	return nil
}

// SearchCategories 搜索食物分类
func (f *foodCategoryLogic) SearchCategories(ctx context.Context, keyword string, page, size int) (*v1.FoodCategoryListResponse, error) {
	// 设置默认分页参数
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 10
	}

	offset := (page - 1) * size

	categories, total, err := f.categoryRepo.Search(keyword, offset, size)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to search categories: %w", err)
	}

	// 转换为响应格式
	records := make([]*v1.FoodCategoryResponse, len(categories))
	for i, category := range categories {
		records[i] = f.convertToCategoryResponse(category)
	}

	return &v1.FoodCategoryListResponse{
		Total:   total,
		Records: records,
		Current: page,
		Size:    size,
	}, nil
}

// convertToCategoryResponse 转换实体为响应格式
func (f *foodCategoryLogic) convertToCategoryResponse(category *entities.FoodCategory) *v1.FoodCategoryResponse {
	return &v1.FoodCategoryResponse{
		ID:          category.ID,
		Name:        category.Name,
		Description: category.Description,
		Color:       category.Color,
		SortOrder:   category.SortOrder,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	}
}
