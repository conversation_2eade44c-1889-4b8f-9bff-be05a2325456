package controllers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/consts"
	"shikeyinxiang/internal/service"
)

// FoodController 食物控制器，处理用户端的食物相关HTTP请求
type FoodController struct {
	foodSvc     service.IFoodService
	categorySvc service.IFoodCategoryService
}

// NewFoodController 创建一个新的 FoodController 实例
func NewFoodController(foodSvc service.IFoodService, categorySvc service.IFoodCategoryService) *FoodController {
	return &FoodController{
		foodSvc:     foodSvc,
		categorySvc: categorySvc,
	}
}

// bindQueryAndValidate 统一处理Query参数绑定和验证
func (fc *FoodController) bindQueryAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindQuery(req); err != nil {
		response.Error(c, consts.CodeParameterError)
		return false
	}
	return true
}

// parseIDParam 解析路径参数中的ID
func (fc *FoodController) parseIDParam(c *gin.Context, paramName string) (int, bool) {
	idStr := c.Param(paramName)
	if idStr == "" {
		response.Error(c, consts.CodeParameterError)
		return 0, false
	}

	id, err := strconv.Atoi(idStr)
	if err != nil || id <= 0 {
		response.Error(c, consts.CodeParameterError)
		return 0, false
	}

	return id, true
}

// GetFoodList 获取食物列表（支持分页、搜索、筛选）
func (fc *FoodController) GetFoodList(c *gin.Context) {
	var req v1.FoodQueryReq
	if !fc.bindQueryAndValidate(c, &req) {
		return
	}

	// 设置默认分页参数
	if req.Current <= 0 {
		req.Current = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	res, err := fc.foodSvc.ListFoods(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	// 使用分页响应格式
	response.SuccessWithPage(c, res.Records, res.Total, res.Current, res.Size)
}

// GetFoodDetail 获取食物详情
func (fc *FoodController) GetFoodDetail(c *gin.Context) {
	id, ok := fc.parseIDParam(c, "id")
	if !ok {
		return
	}

	res, err := fc.foodSvc.GetFood(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// SearchFoods 搜索食物
func (fc *FoodController) SearchFoods(c *gin.Context) {
	var req v1.FoodQueryReq
	if !fc.bindQueryAndValidate(c, &req) {
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	res, err := fc.foodSvc.SearchFoods(c.Request.Context(), req.Keyword, req.CategoryID, req.Page, req.Size)
	if err != nil {
		c.Error(err)
		return
	}

	// 使用分页响应格式
	response.SuccessWithPage(c, res.Records, res.Total, res.Current, res.Size)
}

// GetCategories 获取所有食物分类列表
func (fc *FoodController) GetCategories(c *gin.Context) {
	res, err := fc.categorySvc.GetAllCategories(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// GetCategoryList 获取食物分类列表（支持分页、搜索）
func (fc *FoodController) GetCategoryList(c *gin.Context) {
	var req v1.FoodCategoryQueryReq
	if !fc.bindQueryAndValidate(c, &req) {
		return
	}

	// 设置默认分页参数
	if req.Current <= 0 {
		req.Current = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	res, err := fc.categorySvc.ListCategories(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	// 使用分页响应格式
	response.SuccessWithPage(c, res.Records, res.Total, res.Current, res.Size)
}

// GetCategoryDetail 获取食物分类详情
func (fc *FoodController) GetCategoryDetail(c *gin.Context) {
	id, ok := fc.parseIDParam(c, "id")
	if !ok {
		return
	}

	res, err := fc.categorySvc.GetCategory(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// GetFoodsByCategory 根据分类获取食物列表
func (fc *FoodController) GetFoodsByCategory(c *gin.Context) {
	categoryID, ok := fc.parseIDParam(c, "categoryId")
	if !ok {
		return
	}

	var req v1.FoodQueryReq
	if !fc.bindQueryAndValidate(c, &req) {
		return
	}

	// 设置分类ID
	req.CategoryID = &categoryID

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	res, err := fc.foodSvc.ListFoods(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	// 使用分页响应格式
	response.SuccessWithPage(c, res.Records, res.Total, res.Current, res.Size)
}
