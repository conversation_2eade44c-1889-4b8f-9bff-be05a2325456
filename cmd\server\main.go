package main

import (
	"fmt"
	"log"
	"os"

	"github.com/gin-gonic/gin"

	"shikeyinxiang/internal/cache"
	"shikeyinxiang/internal/config"
	"shikeyinxiang/internal/database"
	"shikeyinxiang/internal/routes"
)

func main() {
	// 加载配置
	configPath := "configs/config.yaml"
	if len(os.Args) > 1 {
		configPath = os.Args[1]
	}

	if err := config.LoadConfig(configPath); err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库
	if err := database.InitMySQL(&config.AppConfig.Database); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.CloseDB()

	// 自动迁移数据库表结构
	if err := database.AutoMigrate(); err != nil {
		log.Fatalf("Failed to auto migrate database: %v", err)
	}

	// 初始化Redis
	if err := cache.InitRedis(&config.AppConfig.Redis); err != nil {
		log.Fatalf("Failed to initialize redis: %v", err)
	}
	defer cache.CloseRedis()

	// 设置Gin模式
	gin.SetMode(config.AppConfig.Server.Mode)

	// 创建Gin引擎
	r := gin.Default()

	// 设置路由
	routes.SetupRoutes(r)

	// 启动服务器
	port := fmt.Sprintf(":%d", config.AppConfig.Server.Port)
	log.Printf("Server starting on port %s", port)

	if err := r.Run(port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
