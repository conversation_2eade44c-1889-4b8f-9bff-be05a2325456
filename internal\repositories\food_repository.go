package repositories

import (
	"errors"
	"fmt"
	"gorm.io/gorm"
	"shikeyinxiang/internal/entities"
	"strconv"
)

// IFoodRepo 定义了食物仓库需要实现的所有方法
type IFoodRepo interface {
	Create(food *entities.Food) error
	GetByID(id int) (*entities.Food, error)
	GetByIDWithCategory(id int) (*entities.Food, error)
	Update(food *entities.Food) error
	Delete(id int) error
	List(offset, limit int) ([]*entities.Food, int64, error)
	ListWithCategory(offset, limit int) ([]*entities.Food, int64, error)
	Search(keyword string, categoryID *int, offset, limit int) ([]*entities.Food, int64, error)
	SearchWithCategory(keyword string, categoryID *int, offset, limit int) ([]*entities.Food, int64, error)
	ExistsByName(name string) (bool, error)
	GetByCategory(categoryID int, offset, limit int) ([]*entities.Food, int64, error)
	CountByCategory(categoryID int) (int64, error)
}

// foodRepository 食物仓储
type foodRepository struct {
	db *gorm.DB
}

// NewFoodRepository 创建食物仓储实例
func NewFoodRepository(db *gorm.DB) IFoodRepo {
	return &foodRepository{
		db: db,
	}
}

// 确保 foodRepository 实现了 IFoodRepo 接口
var _ IFoodRepo = &foodRepository{}

// Create 创建新食物
func (r *foodRepository) Create(food *entities.Food) error {
	if err := r.db.Create(food).Error; err != nil {
		return &DatabaseError{
			Operation: "create",
			Table:     "food",
			Err:       err,
		}
	}
	return nil
}

// GetByID 根据ID获取食物
func (r *foodRepository) GetByID(id int) (*entities.Food, error) {
	var food entities.Food
	if err := r.db.Where("id = ?", id).First(&food).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, &FoodNotFoundError{
				Field: "id",
				Value: strconv.Itoa(id),
			}
		}
		return nil, fmt.Errorf("dao: query food by id=%d: %w", id, err)
	}
	return &food, nil
}

// GetByIDWithCategory 根据ID获取食物（包含分类信息）
func (r *foodRepository) GetByIDWithCategory(id int) (*entities.Food, error) {
	var food entities.Food
	if err := r.db.Preload("Category").Where("id = ?", id).First(&food).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, &FoodNotFoundError{
				Field: "id",
				Value: strconv.Itoa(id),
			}
		}
		return nil, fmt.Errorf("dao: query food with category by id=%d: %w", id, err)
	}
	return &food, nil
}

// Update 更新食物信息
func (r *foodRepository) Update(food *entities.Food) error {
	if err := r.db.Save(food).Error; err != nil {
		return fmt.Errorf("dao: update food id=%d: %w", food.ID, err)
	}
	return nil
}

// Delete 删除食物
func (r *foodRepository) Delete(id int) error {
	if err := r.db.Delete(&entities.Food{}, id).Error; err != nil {
		return fmt.Errorf("dao: delete food id=%d: %w", id, err)
	}
	return nil
}

// List 获取食物列表（分页）
func (r *foodRepository) List(offset, limit int) ([]*entities.Food, int64, error) {
	var foods []*entities.Food
	var total int64

	// 获取总数
	if err := r.db.Model(&entities.Food{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: count foods: %w", err)
	}

	// 获取分页数据
	if err := r.db.Offset(offset).Limit(limit).Order("id DESC").Find(&foods).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: list foods offset=%d limit=%d: %w", offset, limit, err)
	}

	return foods, total, nil
}

// ListWithCategory 获取食物列表（分页，包含分类信息）
func (r *foodRepository) ListWithCategory(offset, limit int) ([]*entities.Food, int64, error) {
	var foods []*entities.Food
	var total int64

	// 获取总数
	if err := r.db.Model(&entities.Food{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: count foods: %w", err)
	}

	// 获取分页数据（包含分类信息）
	if err := r.db.Preload("Category").Offset(offset).Limit(limit).Order("id DESC").Find(&foods).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: list foods with category offset=%d limit=%d: %w", offset, limit, err)
	}

	return foods, total, nil
}

// Search 搜索食物（支持关键词和分类筛选）
func (r *foodRepository) Search(keyword string, categoryID *int, offset, limit int) ([]*entities.Food, int64, error) {
	var foods []*entities.Food
	var total int64

	query := r.db.Model(&entities.Food{})

	// 添加关键词搜索条件
	if keyword != "" {
		query = query.Where("food_name LIKE ?", "%"+keyword+"%")
	}

	// 添加分类筛选条件
	if categoryID != nil {
		query = query.Where("category_id = ?", *categoryID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: count search foods keyword=%s categoryID=%v: %w", keyword, categoryID, err)
	}

	// 获取分页数据
	if err := query.Offset(offset).Limit(limit).Order("id DESC").Find(&foods).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: search foods keyword=%s categoryID=%v offset=%d limit=%d: %w", keyword, categoryID, offset, limit, err)
	}

	return foods, total, nil
}

// SearchWithCategory 搜索食物（支持关键词和分类筛选，包含分类信息）
func (r *foodRepository) SearchWithCategory(keyword string, categoryID *int, offset, limit int) ([]*entities.Food, int64, error) {
	var foods []*entities.Food
	var total int64

	query := r.db.Model(&entities.Food{})

	// 添加关键词搜索条件
	if keyword != "" {
		query = query.Where("food_name LIKE ?", "%"+keyword+"%")
	}

	// 添加分类筛选条件
	if categoryID != nil {
		query = query.Where("category_id = ?", *categoryID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: count search foods with category keyword=%s categoryID=%v: %w", keyword, categoryID, err)
	}

	// 获取分页数据（包含分类信息）
	if err := query.Preload("Category").Offset(offset).Limit(limit).Order("id DESC").Find(&foods).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: search foods with category keyword=%s categoryID=%v offset=%d limit=%d: %w", keyword, categoryID, offset, limit, err)
	}

	return foods, total, nil
}

// ExistsByName 检查食物名称是否已存在
func (r *foodRepository) ExistsByName(name string) (bool, error) {
	var count int64
	if err := r.db.Model(&entities.Food{}).Where("food_name = ?", name).Count(&count).Error; err != nil {
		return false, fmt.Errorf("dao: check food name exists name=%s: %w", name, err)
	}
	return count > 0, nil
}

// GetByCategory 根据分类获取食物列表
func (r *foodRepository) GetByCategory(categoryID int, offset, limit int) ([]*entities.Food, int64, error) {
	var foods []*entities.Food
	var total int64

	query := r.db.Model(&entities.Food{}).Where("category_id = ?", categoryID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: count foods by category categoryID=%d: %w", categoryID, err)
	}

	// 获取分页数据
	if err := query.Offset(offset).Limit(limit).Order("id DESC").Find(&foods).Error; err != nil {
		return nil, 0, fmt.Errorf("dao: get foods by category categoryID=%d offset=%d limit=%d: %w", categoryID, offset, limit, err)
	}

	return foods, total, nil
}

// CountByCategory 统计指定分类下的食物数量
func (r *foodRepository) CountByCategory(categoryID int) (int64, error) {
	var count int64
	if err := r.db.Model(&entities.Food{}).Where("category_id = ?", categoryID).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("dao: count foods by category categoryID=%d: %w", categoryID, err)
	}
	return count, nil
}
